let move_speed = 3, grativy = 0.18;
let bird = document.querySelector('.bird');
let img = document.getElementById('bird-1');
let sound_point = new Audio('sounds effect/point.mp3');
let sound_die = new Audio('sounds effect/die.mp3');
let sound_reward = new Audio('../../../../Sound effects/Reward unlocked.wav');

let sound_background = new Audio('sounds effect/background.mp3');

// Global event listener references for proper cleanup
let globalKeyDownHandler = null;
let gameKeyDownHandler = null;
let gameKeyUpHandler = null;

// Global game variables that need to be reset
let bird_dy = 0;
let pipe_seperation = 0;
let pipe_gap = 35;
let pipes_created = 0;

// Animation frame IDs for cleanup
let moveAnimationId = null;
let gravityAnimationId = null;
let pipeAnimationId = null;

// Chapter progression function
function markChapterCompleted(chapterNumber) {
    const completedChapters = JSON.parse(localStorage.getItem('completedChapters') || '[]');
    if (!completedChapters.includes(chapterNumber)) {
        completedChapters.push(chapterNumber);
        localStorage.setItem('completedChapters', JSON.stringify(completedChapters));
        console.log(`Chapter ${chapterNumber} completed!`);
    }
}
sound_background.loop = true;
sound_background.volume = 0.5;

let bird_props = bird.getBoundingClientRect();
let background = document.querySelector('.background').getBoundingClientRect();
let score_val = document.querySelector('.score_val');
let message = document.querySelector('.message');
let score_title = document.querySelector('.score_title');

let game_state = 'Start';
img.style.display = 'none';
message.classList.add('messageStyle');

// Set initial start message
message.innerHTML = 'Press Enter To Start & Jump<br><span style="color: red;">↑</span> Then use ↑ or SPACE to fly';

// Function to reset to start screen
function resetToStart() {
    console.log('Resetting to start screen...'); // Debug

    // Stop all existing animation frames
    if (moveAnimationId) {
        cancelAnimationFrame(moveAnimationId);
        moveAnimationId = null;
    }
    if (gravityAnimationId) {
        cancelAnimationFrame(gravityAnimationId);
        gravityAnimationId = null;
    }
    if (pipeAnimationId) {
        cancelAnimationFrame(pipeAnimationId);
        pipeAnimationId = null;
    }

    // Clean up game event listeners
    if (gameKeyDownHandler) {
        document.removeEventListener('keydown', gameKeyDownHandler);
        gameKeyDownHandler = null;
    }
    if (gameKeyUpHandler) {
        document.removeEventListener('keyup', gameKeyUpHandler);
        gameKeyUpHandler = null;
    }

    // Reset variables
    bird_dy = 0;
    pipe_seperation = 0;
    pipes_created = 0;

    // Clean up existing pipes
    document.querySelectorAll('.pipe_sprite').forEach((e) => e.remove());

    // Reset to start screen
    img.style.display = 'none';
    bird.style.top = '40vh';

    // Reset game state to Start
    game_state = 'Start';
    message.innerHTML = 'Press Enter To Start & Jump<br><span style="color: red;">↑</span> Then use ↑ or SPACE to fly';
    message.classList.add('messageStyle');
    score_title.innerHTML = '';
    score_val.innerHTML = '';

    // Stop background music
    sound_background.pause();
    sound_background.currentTime = 0;
}

// Function to start the actual game
function startGame() {
    console.log('Starting game...'); // Debug

    // Reset variables (in case called directly)
    bird_dy = -5.5; // Give initial jump when starting with Enter (same as Space/Arrow Up)
    pipe_seperation = 0;
    pipes_created = 0;

    // Clean up existing pipes
    document.querySelectorAll('.pipe_sprite').forEach((e) => e.remove());

    // Reset bird
    img.style.display = 'block';
    img.src = 'images/flappybird-01.png'; // Set to jumping sprite
    bird.style.top = '40vh';

    // Reset game state
    game_state = 'Play';
    message.innerHTML = '';
    score_title.innerHTML = 'Score : ';
    score_val.innerHTML = '0';
    message.classList.remove('messageStyle');

    // Try to play background music
    sound_background.play().catch(() => {
        console.log('Audio blocked by browser');
    });

    // Start game
    play();
}

// Simple global event listener for Enter key
document.addEventListener('keydown', (e) => {
    console.log('Key pressed:', e.key, 'Game state:', game_state); // Debug
    if (e.key == 'Enter' && game_state != 'Play') {
        if (game_state == 'Start') {
            startGame(); // Start the game from start screen
        } else if (game_state == 'End') {
            resetToStart(); // Reset to start screen after game over
        }
    }
});

function play() {
    // Clean up any existing game event listeners
    function cleanupGameEventListeners() {
        if (gameKeyDownHandler) {
            document.removeEventListener('keydown', gameKeyDownHandler);
            gameKeyDownHandler = null;
        }
        if (gameKeyUpHandler) {
            document.removeEventListener('keyup', gameKeyUpHandler);
            gameKeyUpHandler = null;
        }
    }

    function move() {
        if (game_state != 'Play') return;

        let pipe_sprite = document.querySelectorAll('.pipe_sprite');
        pipe_sprite.forEach((element) => {
            let pipe_sprite_props = element.getBoundingClientRect();
            bird_props = bird.getBoundingClientRect();

            if (pipe_sprite_props.right <= 0) {
                element.remove();
            } else {
                // Collision detection
                if (
                    bird_props.left < pipe_sprite_props.left + pipe_sprite_props.width &&
                    bird_props.left + bird_props.width > pipe_sprite_props.left &&
                    bird_props.top < pipe_sprite_props.top + pipe_sprite_props.height &&
                    bird_props.top + bird_props.height > pipe_sprite_props.top
                ) {
                    console.log('Game Over - Collision detected'); // Debug
                    game_state = 'End';
                    message.innerHTML = 'Game Over'.fontcolor('red') + '<br>Press Enter To Return to Start';
                    message.classList.add('messageStyle');
                    img.style.display = 'none';
                    sound_die.play().catch(() => {
                        // Ignore audio errors
                    });
                    console.log('Game state set to:', game_state); // Debug
                    return;
                } else {
                    // Score update
                    if (
                        pipe_sprite_props.right < bird_props.left &&
                        pipe_sprite_props.right + move_speed >= bird_props.left &&
                        element.increase_score == '1'
                    ) {
                        let currentScore = parseInt(score_val.innerHTML);
                        currentScore += 1;
                        score_val.innerHTML = currentScore;
                        element.increase_score = '0';
                        sound_point.play().catch(() => {
                            // Ignore audio errors
                        });

                        // Check for win condition immediately when score reaches 5
                        if (currentScore >= 5) {
                            // Mark Chapter 1 as completed
                            markChapterCompleted(1);

                            // Stop the game
                            game_state = 'End';

                            // Play reward sound
                            sound_reward.currentTime = 0;
                            sound_reward.play().catch(() => {
                                // Ignore audio errors
                            });

                            // Create a popup overlay with options
                            let overlay = document.createElement('div');
                            overlay.innerHTML = `
                                <div style="font-size: 12px; text-align: center; font-family: 'Press Start 2P', cursive;">
                                    🎉 <b>Congrats! You have completed the game.</b><br><br>
                                    🎁 <b>Here's your reward:</b> 🎁<br>
                                    <div style="margin: 15px 0;">
                                        <img src="images/IMG_3184.gif" alt="Reward" style="width: 120px; height: 120px; border-radius: 10px; border: 3px solid #FFD700;">
                                    </div>
                                    <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                                        <button id="nextChapterBtn" style="padding: 10px 15px; cursor: pointer; background: #4CAF50; color: white; border: none; border-radius: 5px; font-family: 'Press Start 2P', cursive; font-size: 10px;">Proceed to Next Chapter</button>
                                        <button id="exitBtn" style="padding: 10px 15px; cursor: pointer; background: #f44336; color: white; border: none; border-radius: 5px; font-family: 'Press Start 2P', cursive; font-size: 10px;">Exit Game</button>
                                    </div>
                                </div>
                            `;
                            overlay.style.position = 'fixed';
                            overlay.style.top = '50%';
                            overlay.style.left = '50%';
                            overlay.style.transform = 'translate(-50%, -50%)';
                            overlay.style.background = 'white';
                            overlay.style.padding = '20px';
                            overlay.style.borderRadius = '10px';
                            overlay.style.zIndex = '1000';
                            overlay.style.boxShadow = 'rgba(0, 0, 0, 0.24) 0px 3px 8px';
                            overlay.style.border = '3px solid #333';
                            document.body.appendChild(overlay);

                            // Add event listeners to buttons
                            setTimeout(() => {
                                // Next Chapter button
                                document.getElementById('nextChapterBtn').addEventListener('click', function() {
                                    // Create loading screen before redirecting to chapter selection
                                    const loadingOverlay = document.createElement('div');
                                    loadingOverlay.style.cssText = `
                                        position: fixed;
                                        top: 0;
                                        left: 0;
                                        width: 100%;
                                        height: 100%;
                                        background-image: url('../../../../images/backdrop stars 2-01.png');
                                        background-size: cover;
                                        background-position: center;
                                        background-repeat: no-repeat;
                                        display: flex;
                                        flex-direction: column;
                                        justify-content: center;
                                        align-items: center;
                                        z-index: 10000;
                                        color: white;
                                        font-family: 'Press Start 2P', cursive;
                                    `;

                                    loadingOverlay.innerHTML = `
                                        <div style="text-align: center;">
                                            <div style="font-size: 24px; margin-bottom: 30px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                                                Returning to Chapters
                                            </div>
                                            <div style="font-size: 14px; opacity: 0.8;">
                                                CHAPTER SELECTION
                                            </div>
                                            <div style="font-size: 12px; opacity: 0.6; margin-top: 10px;">
                                                Choose your next adventure...
                                            </div>
                                        </div>
                                    `;



                                    document.body.appendChild(loadingOverlay);

                                    // Redirect to chapter selection after 3 seconds
                                    setTimeout(() => {
                                        window.location.href = '../../chapter.html';
                                    }, 3000);
                                });

                                // Exit button
                                document.getElementById('exitBtn').addEventListener('click', function() {
                                    document.body.removeChild(overlay);
                                    // End the game and show restart message
                                    game_state = 'End';
                                    message.innerHTML = 'Game Over'.fontcolor('red') + '<br>Press Enter To Return to Start';
                                    message.classList.add('messageStyle');
                                    img.style.display = 'none';
                                    // Game will be reset when user presses Enter
                                });
                            }, 100); // Small timeout to ensure DOM elements are available
                            return;
                        }
                    }
                    element.style.left = pipe_sprite_props.left - move_speed + 'px';
                }
            }
        });
        moveAnimationId = requestAnimationFrame(move);
    }
    moveAnimationId = requestAnimationFrame(move);

    function apply_gravity() {
        if (game_state != 'Play') return;

        bird_dy = bird_dy + grativy;

        // Setup game event listeners only once
        if (!gameKeyDownHandler) {
            gameKeyDownHandler = (e) => {
                if ((e.key == 'ArrowUp' || e.key == ' ') && game_state == 'Play') {
                    img.src = 'images/flappybird-01.png';
                    bird_dy = -5.5;
                }
            };

            gameKeyUpHandler = (e) => {
                if ((e.key == 'ArrowUp' || e.key == ' ') && game_state == 'Play') {
                    img.src = 'images/flappybird-02.png';
                }
            };

            document.addEventListener('keydown', gameKeyDownHandler);
            document.addEventListener('keyup', gameKeyUpHandler);
        }

        if (bird_props.top <= 0 || bird_props.bottom >= background.bottom) {
            console.log('Game Over - Boundary collision'); // Debug
            game_state = 'End';
            message.innerHTML = 'Game Over'.fontcolor('red') + '<br>Press Enter To Return to Start';
            message.classList.add('messageStyle');
            img.style.display = 'none';
            sound_die.play().catch(() => {
                // Ignore audio errors
            });
            console.log('Game state set to:', game_state); // Debug
            return;
        }



        bird.style.top = bird_props.top + bird_dy + 'px';
        bird_props = bird.getBoundingClientRect();
        gravityAnimationId = requestAnimationFrame(apply_gravity);
    }
    gravityAnimationId = requestAnimationFrame(apply_gravity);

    function create_pipe() {
        if (game_state != 'Play') return;

        // Stop creating new pipes after 5 pipes have been created
        if (pipes_created >= 5) return;

        if (pipe_seperation > 130) {
            pipe_seperation = 0;

            let pipe_posi = Math.floor(Math.random() * 50) + 5;

            // Create upper pipe
            let pipe_sprite_upper = document.createElement('div');
            pipe_sprite_upper.className = 'pipe_sprite';
            pipe_sprite_upper.style.top = '0vh';
            pipe_sprite_upper.style.height = pipe_posi + 'vh';
            pipe_sprite_upper.style.left = '100vw';
            document.body.appendChild(pipe_sprite_upper);

            // Create lower pipe (existing code)
            let pipe_sprite = document.createElement('div');
            pipe_sprite.className = 'pipe_sprite';
            pipe_sprite.style.top = pipe_posi + pipe_gap + 'vh';
            pipe_sprite.style.left = '100vw';
            pipe_sprite.increase_score = '1';
            document.body.appendChild(pipe_sprite);

            pipes_created++; // Increment pipe count
        }

        pipe_seperation++;
        pipeAnimationId = requestAnimationFrame(create_pipe);
    }

    pipeAnimationId = requestAnimationFrame(create_pipe);
}

// Reset and prepare music (without autoplay)
sound_background.loop = true;
sound_background.volume = 0.5;
sound_background.currentTime = 0;












