// Ending page JavaScript

// Sound manager for chorten background music and reward sound
let chortenSound;
let rewardSound;

const chortenSoundPaths = [
    "../../../Sound effects/chorten.mp3",
    "../../Sound effects/chorten.mp3",
    "../Sound effects/chorten.mp3",
    "./Sound effects/chorten.mp3",
    "Sound effects/chorten.mp3"
];

const rewardSoundPaths = [
    "../../../Sound effects/Reward unlocked.wav",
    "../../Sound effects/Reward unlocked.wav",
    "../Sound effects/Reward unlocked.wav",
    "./Sound effects/Reward unlocked.wav",
    "Sound effects/Reward unlocked.wav"
];

// Load and continue chorten sound
function loadAndContinueChortenSound() {
    // Check if audio is already playing from previous page
    if (window.globalChortenAudio && !window.globalChortenAudio.paused) {
        console.log('Using existing chorten audio - seamless continuation');
        chortenSound = window.globalChortenAudio;
        return;
    }

    // If no existing audio, load new one (fallback)
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= chortenSoundPaths.length) {
            console.error('Could not load chorten sound from any path');
            return;
        }

        chortenSound = new Audio(chortenSoundPaths[pathIndex]);
        chortenSound.preload = "auto";
        chortenSound.volume = 0.6;
        chortenSound.loop = true;

        chortenSound.addEventListener('canplaythrough', () => {
            console.log(`Chorten sound loaded successfully from: ${chortenSoundPaths[pathIndex]}`);
            // Only start playing if it should be playing
            if (sessionStorage.getItem('chortenMusicPlaying') === 'true') {
                chortenSound.play().then(() => {
                    console.log('Chorten sound started playing');
                }).catch(error => {
                    console.log('Could not start chorten sound:', error);
                });
            }
        });

        chortenSound.addEventListener('error', (e) => {
            console.log(`Failed to load chorten sound from path ${pathIndex}: ${chortenSoundPaths[pathIndex]}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Load reward sound
function loadRewardSound() {
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= rewardSoundPaths.length) {
            console.error('Could not load reward sound from any path');
            return;
        }

        rewardSound = new Audio(rewardSoundPaths[pathIndex]);
        rewardSound.preload = "auto";
        rewardSound.volume = 0.8;

        rewardSound.addEventListener('canplaythrough', () => {
            console.log(`Reward sound loaded successfully from: ${rewardSoundPaths[pathIndex]}`);
        });

        rewardSound.addEventListener('error', (e) => {
            console.log(`Failed to load reward sound from path ${pathIndex}: ${rewardSoundPaths[pathIndex]}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Play reward sound and fade out chorten music smoothly
function playRewardSound() {
    // Fade out chorten music smoothly when reward is earned
    if (chortenSound && !chortenSound.paused) {
        console.log('Fading out chorten music - reward earned!');

        // Smooth fade out over 2 seconds
        const fadeOutInterval = setInterval(() => {
            if (chortenSound.volume > 0.05) {
                chortenSound.volume -= 0.05;
            } else {
                chortenSound.pause();
                chortenSound.currentTime = 0;
                clearInterval(fadeOutInterval);
                console.log('Chorten music faded out completely');
            }
        }, 100);

        // Clear session storage
        sessionStorage.removeItem('chortenMusicPlaying');
        sessionStorage.removeItem('chortenMusicTime');
        // Clear global reference
        window.globalChortenAudio = null;
    }

    // Play reward sound after a brief moment
    setTimeout(() => {
        if (rewardSound) {
            rewardSound.currentTime = 0;
            rewardSound.play().then(() => {
                console.log('Reward sound played successfully');
            }).catch(error => {
                console.log('Could not play reward sound:', error);
            });
        }
    }, 500); // Small delay to let fade out start
}

document.addEventListener('DOMContentLoaded', function() {
    // Load chorten sound (continue from previous page) and reward sound
    loadAndContinueChortenSound();
    loadRewardSound();

    // Fade out black screen slowly after page loads
    const blackScreen = document.getElementById('black-screen-overlay');
    if (blackScreen) {
        setTimeout(() => {
            blackScreen.classList.add('fade-out');
            // Remove the overlay after fade completes
            setTimeout(() => {
                blackScreen.remove();
                // Play reward sound when the congratulations screen is fully visible
                // This will also stop the chorten music
                setTimeout(() => {
                    playRewardSound();
                }, 500);
            }, 2000);
        }, 500);
    }

    // Create sparkle effects around the relic
    createSparkleEffect();

    // Add click interaction to the relic
    const relicImage = document.querySelector('.relic-image');
    if (relicImage) {
        relicImage.addEventListener('click', function() {
            createBurstEffect(this);
        });
    }

    // Add click handler for next button
    const nextButton = document.getElementById('next-button');
    if (nextButton) {
        nextButton.addEventListener('click', function() {
            redirectToArrival();
        });
    }

    // Enable audio context on first user interaction (for browser autoplay policies)
    document.addEventListener('click', function enableAudio() {
        if (chortenSound) {
            // Try to play and immediately pause to enable audio context
            chortenSound.play().then(() => {
                chortenSound.pause();
                chortenSound.currentTime = parseFloat(sessionStorage.getItem('chortenMusicTime') || '0');
            }).catch(() => {
                // Ignore errors during audio context initialization
            });
        }
        if (rewardSound) {
            // Try to play and immediately pause to enable audio context
            rewardSound.play().then(() => {
                rewardSound.pause();
                rewardSound.currentTime = 0;
            }).catch(() => {
                // Ignore errors during audio context initialization
            });
        }
        // Remove this listener after first interaction
        document.removeEventListener('click', enableAudio);
    }, { once: true });
});

// Create continuous sparkle effect
function createSparkleEffect() {
    const relicContainer = document.querySelector('.relic-container');
    if (!relicContainer) return;
    
    setInterval(() => {
        createSparkle(relicContainer);
    }, 500);
}

// Create individual sparkle
function createSparkle(container) {
    const sparkle = document.createElement('div');
    sparkle.className = 'sparkle';
    
    // Random position around the relic
    const rect = container.getBoundingClientRect();
    const x = Math.random() * rect.width;
    const y = Math.random() * rect.height;
    
    sparkle.style.cssText = `
        position: absolute;
        left: ${x}px;
        top: ${y}px;
        width: 4px;
        height: 4px;
        background: #FFD700;
        border-radius: 50%;
        pointer-events: none;
        animation: sparkleAnimation 1.5s ease-out forwards;
        box-shadow: 0 0 6px #FFD700;
    `;
    
    container.appendChild(sparkle);
    
    // Remove sparkle after animation
    setTimeout(() => {
        if (sparkle.parentNode) {
            sparkle.parentNode.removeChild(sparkle);
        }
    }, 1500);
}

// Create burst effect when relic is clicked
function createBurstEffect(element) {
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    // Create multiple particles
    for (let i = 0; i < 20; i++) {
        setTimeout(() => {
            createBurstParticle(centerX, centerY);
        }, i * 50);
    }
}

// Create individual burst particle
function createBurstParticle(centerX, centerY) {
    const particle = document.createElement('div');
    
    // Random direction and distance
    const angle = Math.random() * Math.PI * 2;
    const distance = 50 + Math.random() * 100;
    const tx = Math.cos(angle) * distance;
    const ty = Math.sin(angle) * distance;
    
    particle.style.cssText = `
        position: fixed;
        left: ${centerX}px;
        top: ${centerY}px;
        width: 6px;
        height: 6px;
        background: #FFD700;
        border-radius: 50%;
        pointer-events: none;
        z-index: 1000;
        box-shadow: 0 0 10px #FFD700;
        animation: burstAnimation 2s ease-out forwards;
        --tx: ${tx}px;
        --ty: ${ty}px;
    `;
    
    document.body.appendChild(particle);
    
    // Remove particle after animation
    setTimeout(() => {
        if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
        }
    }, 2000);
}

// Add CSS animations dynamically
const style = document.createElement('style');
style.textContent = `
    @keyframes sparkleAnimation {
        0% {
            opacity: 0;
            transform: scale(0);
        }
        50% {
            opacity: 1;
            transform: scale(1);
        }
        100% {
            opacity: 0;
            transform: scale(0);
        }
    }
    
    @keyframes burstAnimation {
        0% {
            opacity: 1;
            transform: translate(0, 0) scale(1);
        }
        100% {
            opacity: 0;
            transform: translate(var(--tx), var(--ty)) scale(0);
        }
    }
`;
document.head.appendChild(style);

// Function to handle redirect to arrival with black screen transition
function redirectToArrival() {
    // Create black screen overlay for transition
    const fadeOverlay = document.createElement('div');
    fadeOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: black;
        opacity: 0;
        z-index: 10000;
        transition: opacity 1.5s ease-in-out;
        pointer-events: none;
    `;
    document.body.appendChild(fadeOverlay);

    // Trigger fade in
    setTimeout(() => {
        fadeOverlay.style.opacity = '1';
    }, 50);

    // Redirect after fade completes
    setTimeout(() => {
        window.location.href = '../arrival/arrival.html';
    }, 1800); // 1.8 seconds to allow fade to complete
}
