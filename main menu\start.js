// Sound manager for menu clicks
let menuClickSound;
const menuSoundPaths = [
    "../Sound effects/Click menu and settings .mp3",
    "./Sound effects/Click menu and settings .mp3",
    "Sound effects/Click menu and settings .mp3",
    "../Sound effects/click-menu.mp3",
    "./Sound effects/click-menu.mp3",
    "Sound effects/click-menu.mp3"
];

// Load menu click sound
function loadMenuClickSound() {
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= menuSoundPaths.length) {
            console.error('Could not load menu click sound from any path');
            return;
        }

        menuClickSound = new Audio(menuSoundPaths[pathIndex]);
        menuClickSound.preload = "auto";
        menuClickSound.volume = 0.7;

        menuClickSound.addEventListener('canplaythrough', () => {
            console.log(`Menu click sound loaded successfully from: ${menuSoundPaths[pathIndex]}`);
        });

        menuClickSound.addEventListener('error', (e) => {
            console.log(`Failed to load menu sound from path ${pathIndex}: ${menuSoundPaths[pathIndex]}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Play menu click sound
function playMenuClickSound() {
    if (menuClickSound) {
        menuClickSound.currentTime = 0;
        menuClickSound.play().catch(error => {
            console.log('Could not play menu click sound:', error);
        });
    }
}



// Wait for DOM to load
document.addEventListener('DOMContentLoaded', function() {
    // Load menu click sound
    loadMenuClickSound();



    // Enable audio context on first user interaction
    document.addEventListener('click', function enableAudio() {
        if (menuClickSound) {
            // Try to play and immediately pause to enable audio context
            menuClickSound.play().then(() => {
                menuClickSound.pause();
                menuClickSound.currentTime = 0;
            }).catch(() => {
                // Ignore errors during audio context initialization
            });
        }



        // Remove this listener after first interaction
        document.removeEventListener('click', enableAudio);
    }, { once: true });

    // Switch to main menu when start is pressed
    document.getElementById('startButton').addEventListener('click', function() {
        playMenuClickSound();
        document.getElementById('startScreen').style.display = 'none';
        document.getElementById('mainMenu').style.display = 'flex';
    });

    // Handle menu button clicks
    document.querySelectorAll('.menu-button').forEach(button => {
        button.addEventListener('click', function() {
            playMenuClickSound();
            const action = this.textContent.trim();

            switch(action) {
                case 'START GAME':
                    window.location.href = 'chapter/chapter.html';
                    break;
                case 'PLAYER PROFILE':
                    // Add profile screen logic
                    break;
                case 'CHARACTER DESIGN':
                    // Add character design logic
                    break;
                case 'SETTINGS':
                    // Add settings logic
                    break;
            }
        });
    });
});
