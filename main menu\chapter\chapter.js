// Sound manager for menu clicks
let menuClickSound;
const menuSoundPaths = [
    "../Sound effects/Click menu and settings .mp3",
    "./Sound effects/Click menu and settings .mp3",
    "Sound effects/Click menu and settings .mp3",
    "../Sound effects/click-menu.mp3",
    "./Sound effects/click-menu.mp3",
    "Sound effects/click-menu.mp3"
];

// Load menu click sound
function loadMenuClickSound() {
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= menuSoundPaths.length) {
            console.error('Could not load menu click sound from any path');
            return;
        }

        menuClickSound = new Audio(menuSoundPaths[pathIndex]);
        menuClickSound.preload = "auto";
        menuClickSound.volume = 0.7;

        menuClickSound.addEventListener('canplaythrough', () => {
            console.log(`Menu click sound loaded successfully from: ${menuSoundPaths[pathIndex]}`);
        });

        menuClickSound.addEventListener('error', (e) => {
            console.log(`Failed to load menu sound from path ${pathIndex}: ${menuSoundPaths[pathIndex]}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Play menu click sound
function playMenuClickSound() {
    if (menuClickSound) {
        menuClickSound.currentTime = 0;
        menuClickSound.play().catch(error => {
            console.log('Could not play menu click sound:', error);
        });
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Load menu click sound
    loadMenuClickSound();

    const levelCircles = document.querySelectorAll('.level-circle');
    const currentLevelDisplay = document.getElementById('currentLevel');
    const levelDescription = document.getElementById('levelDescription');

    // Initialize chapter progression system
    initializeChapterProgression();

    // Setup initial event listeners
    setupEventListeners();

    // Add animation for initial display
    currentLevelDisplay.style.width = '100px';
    currentLevelDisplay.style.height = '100px';
    levelDescription.textContent = `CHOOSE A CHAPTER`;

    // Add click sound to back button
    const backButton = document.querySelector('.back-button');
    if (backButton) {
        backButton.addEventListener('click', function() {
            playMenuClickSound();
        });
    }
});

function setupEventListeners() {
    const levelCircles = document.querySelectorAll('.level-circle');
    const currentLevelDisplay = document.getElementById('currentLevel');
    const levelDescription = document.getElementById('levelDescription');

    // Level descriptions
    const levelData = {
        1: {
            name: "THE VILLAGE'S LAMENT",
            description: 'BEGINNING OF YOUR JOURNEY. PURE AND FULL OF POTENTIAL.',
            link: './Chapter1/lhamo.html'
        },
        2: {
            name: 'UNVEILING THE SHADOWS',
            description: 'THE SINMO\'S HIDDEN TRUTHS',
            link: './Chapter2/house.html'
        },
        3: {
            name: 'THE FIERY ABYSS',
            description: 'GROWTH AND PROSPERITY AWAIT IN THIS STAGE.',
        },
        4: {
            name: 'VERDANT GROVE',
            description: 'FIERY TEST OF YOUR TRUE SKILLS BEGINS HERE.',
        },
        5: {
            name: 'THE UNDERWORLD DEPTHS',
            description: 'GOLDEN PINNACLE OF ACHIEVEMENT. ONLY THE WORTHY REACH HERE.',
        }
    };

    // Variable to track the last hovered level
    let lastHoveredLevel = null;

    // Clear existing event listeners by cloning and replacing elements
    levelCircles.forEach(circle => {
        const newCircle = circle.cloneNode(true);
        circle.parentNode.replaceChild(newCircle, circle);
    });

    // Get the updated circles after replacement
    const updatedCircles = document.querySelectorAll('.level-circle');

    updatedCircles.forEach(circle => {
        const level = circle.getAttribute('data-level');
        const isLocked = circle.classList.contains('locked');

        // Skip adding hover effects for locked levels
        if (isLocked) {
            circle.addEventListener('mouseenter', () => {
                // For locked levels, just show a locked message
                levelDescription.textContent = `CHAPTER ${level}: LOCKED - COMPLETE PREVIOUS CHAPTERS TO UNLOCK`;
            });

            circle.addEventListener('mouseleave', () => {
                // Reset description when mouse leaves
                if (lastHoveredLevel) {
                    // Show the last hovered level description
                    levelDescription.textContent = `CHAPTER ${lastHoveredLevel}: ${levelData[lastHoveredLevel].name} - ${levelData[lastHoveredLevel].description}`;
                } else {
                    levelDescription.textContent = 'CHOOSE A CHAPTER!';
                }
            });
            return;
        }

        // Regular hover effects for unlocked levels
        circle.addEventListener('mouseenter', () => {
            // Get computed style from the circle
            const circleStyle = window.getComputedStyle(circle);
            const backgroundColor = circleStyle.backgroundColor;

            // Check if the circle has an image
            const img = circle.querySelector('img');
            const hasValidImage = img && img.src && img.src !== '' && !img.src.includes('./images/.png');

            // Update current level display
            if (hasValidImage) {
                currentLevelDisplay.style.backgroundImage = `url('${img.src}')`;
                currentLevelDisplay.style.backgroundSize = 'cover';
                currentLevelDisplay.style.backgroundColor = 'transparent';
            } else {
                currentLevelDisplay.style.backgroundImage = 'none';
                currentLevelDisplay.style.backgroundColor = backgroundColor;
            }
            currentLevelDisplay.style.border = '2px solid #000';

            // Update description
            levelDescription.textContent = `CHAPTER ${level}: ${levelData[level].name} - ${levelData[level].description}`;

            // Update last hovered level
            lastHoveredLevel = level;

            // Add pulse animation
            currentLevelDisplay.style.animation = 'pixelPulse 0.3s steps(2)';
        });

        circle.addEventListener('mouseleave', () => {
            // Remove pulse animation
            currentLevelDisplay.style.animation = 'none';
            currentLevelDisplay.style.filter = 'none';
            // Keep the description of the last hovered level
        });
    });

    // Add click event for unlocked levels
    updatedCircles.forEach(circle => {
        const level = circle.getAttribute('data-level');
        const isLocked = circle.classList.contains('locked');

        if (!isLocked) {
            circle.addEventListener('click', () => {
                playMenuClickSound(); // Play click sound when selecting chapter



                // Navigate to the chapter if it has a link
                if (levelData[level] && levelData[level].link) {
                    window.location.href = levelData[level].link;
                }
            });

            // Add cursor pointer style to indicate clickable
            circle.style.cursor = 'pointer';
        }
    });
}

// Chapter progression system
function initializeChapterProgression() {
    const completedChapters = getCompletedChapters();
    updateChapterLocks(completedChapters);
}

function getCompletedChapters() {
    const completed = localStorage.getItem('completedChapters');
    return completed ? JSON.parse(completed) : [];
}

function markChapterCompleted(chapterNumber) {
    const completedChapters = getCompletedChapters();
    if (!completedChapters.includes(chapterNumber)) {
        completedChapters.push(chapterNumber);
        localStorage.setItem('completedChapters', JSON.stringify(completedChapters));
        updateChapterLocks(completedChapters);
    }
}

function updateChapterLocks(completedChapters) {
    const levelCircles = document.querySelectorAll('.level-circle');

    levelCircles.forEach(circle => {
        const level = parseInt(circle.getAttribute('data-level'));

        // Mark completed chapters
        if (completedChapters.includes(level)) {
            circle.classList.add('completed');
        } else {
            circle.classList.remove('completed');
        }

        // Chapter 1 is always unlocked
        if (level === 1) {
            circle.classList.remove('locked');
            return;
        }

        // Check if previous chapter is completed
        const previousChapter = level - 1;
        if (completedChapters.includes(previousChapter)) {
            circle.classList.remove('locked');
            circle.style.cursor = 'pointer';
        } else {
            circle.classList.add('locked');
            circle.style.cursor = 'default';
        }
    });

    // Re-setup event listeners after updating locks
    setupEventListeners();
}

// Debug function to reset progress (for testing)
function resetChapterProgress() {
    localStorage.removeItem('completedChapters');
    location.reload();
}

// Test function to simulate Chapter 1 completion (for testing)
function testCompleteChapter1() {
    markChapterCompleted(1);
    console.log('Chapter 1 marked as completed for testing');
}

// Make functions available globally for testing
window.resetChapterProgress = resetChapterProgress;
window.testCompleteChapter1 = testCompleteChapter1;

// Add pixel pulse animation
const style = document.createElement('style');
style.textContent = `
    @keyframes pixelPulse {
        0% { transform: translateY(0); }
        50% { transform: translateY(-4px); }
        100% { transform: translateY(0); }
    }
`;
document.head.appendChild(style);
