// Game elements
const canvas = document.getElementById("gameCanvas");
const ctx = canvas.getContext("2d");
const messageDiv = document.getElementById("message");
const closeButton = document.getElementById("close-message");

// Chorten background music
let chortenSound;
const chortenSoundPaths = [
    "../../../Sound effects/chorten.mp3",
    "../../Sound effects/chorten.mp3",
    "../Sound effects/chorten.mp3",
    "./Sound effects/chorten.mp3",
    "Sound effects/chorten.mp3"
];

// Load chorten sound
function loadChortenSound() {
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= chortenSoundPaths.length) {
            console.error('Could not load chorten sound from any path');
            return;
        }

        chortenSound = new Audio(chortenSoundPaths[pathIndex]);
        chortenSound.preload = "auto";
        chortenSound.volume = 0.6;
        chortenSound.loop = true; // Loop until reward is earned

        // Optimize for smooth playback
        chortenSound.crossOrigin = "anonymous";
        chortenSound.load(); // Force load

        chortenSound.addEventListener('canplaythrough', () => {
            console.log(`Chorten sound loaded successfully from: ${chortenSoundPaths[pathIndex]}`);
        });

        chortenSound.addEventListener('error', (e) => {
            console.log(`Failed to load chorten sound from path ${pathIndex}: ${chortenSoundPaths[pathIndex]}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Play chorten sound
function playChortenSound() {
    if (chortenSound) {
        chortenSound.currentTime = 0;
        chortenSound.play().then(() => {
            console.log('Chorten sound started playing');
            // Store in sessionStorage that chorten music is playing
            sessionStorage.setItem('chortenMusicPlaying', 'true');
        }).catch(error => {
            console.log('Could not play chorten sound:', error);
        });
    }
}

// Game images
const images = {
    background: new Image(),
    girlWalk: new Image(),
    dorji: new Image(),
    key: new Image(),
    lamp: new Image()
};

// Set image sources
images.background.src = "landchorten.png";
images.girlWalk.src = "girlwalk.png";
images.dorji.src = "dorji.png";
images.key.src = "key.png";
images.lamp.src = "lamp.png";

// Game objects
const girl = {
    x: -100,
    y: 470,
    width: 80,
    height: 190,
    speed: 3,
    stopX: 1000,
    hasStopped: false
};

const treasures = [
    { x: 1090, y: 450, width: 50, height: 50, type: "dorji", bounce: 0, bounceSpeed: 0.05 },
    { x: 1090, y: 500, width: 50, height: 50, type: "key", bounce: 0, bounceSpeed: 0.07 },
    { x: 1090, y: 390, width: 50, height: 50, type: "lamp", bounce: 0, bounceSpeed: 0.06 }
];

// Game state
let assetsLoaded = 0;
const totalAssets = Object.keys(images).length;

// Initialize game when all images are loaded
Object.values(images).forEach(img => {
    img.onload = () => {
        assetsLoaded++;
        if (assetsLoaded === totalAssets) {
            initGame();
        }
    };
});

function initGame() {
    canvas.width = images.background.width;
    canvas.height = images.background.height;

    // Load and start chorten music
    loadChortenSound();

    // Fade out black screen slowly after page loads
    const blackScreen = document.getElementById('black-screen-overlay');
    if (blackScreen) {
        setTimeout(() => {
            blackScreen.classList.add('fade-out');
            // Remove the overlay after fade completes
            setTimeout(() => {
                blackScreen.remove();
                // Start chorten music after fade completes
                setTimeout(() => {
                    playChortenSound();
                }, 500);
            }, 2000);
        }, 500);
    }

    gameLoop();
}

function drawGirl() {
    // Smooth walking with no vertical movement
    ctx.drawImage(
        images.girlWalk, 
        girl.x, 
        girl.y, 
        girl.width, 
        girl.height
    );
}

function drawTreasures() {
    treasures.forEach(treasure => {
        const img = images[treasure.type];
        
        // Update bounce position
        treasure.bounce += treasure.bounceSpeed;
        const bounceHeight = Math.sin(treasure.bounce) * 7;
        
        // Draw treasure with bounce effect
        ctx.drawImage(
            img,
            treasure.x,
            treasure.y + bounceHeight,
            treasure.width,
            treasure.height
        );
        
        // Glitter effect (sparkles)
        if (Math.random() > 0.9) {
            ctx.save();
            ctx.globalAlpha = 0.7;
            ctx.fillStyle = `rgba(255, 255, 255, ${Math.random() * 0.8})`;
            ctx.beginPath();
            ctx.arc(
                treasure.x + Math.random() * treasure.width,
                treasure.y + Math.random() * treasure.height + bounceHeight,
                Math.random() * 4 + 2,
                0,
                Math.PI * 2
            );
            ctx.fill();
            ctx.restore();
        }
    });
}

function update() {
    // Move girl until stopX is reached
    if (girl.x < girl.stopX) {
        girl.x += girl.speed;
    }
    // Show message when stopped
    else if (!girl.hasStopped) {
        girl.hasStopped = true;
        setTimeout(() => {
            messageDiv.style.display = "block";
        }, 500);
    }

    // Update treasure bounce animations
    treasures.forEach(treasure => {
        treasure.bounce += treasure.bounceSpeed;
    });
}

function gameLoop() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw background
    ctx.drawImage(images.background, 0, 0, canvas.width, canvas.height);
    
    // Draw game elements
    drawTreasures();
    drawGirl();
    
    update();
    requestAnimationFrame(gameLoop);
}

// Enable audio context on first user interaction
document.addEventListener('click', function enableAudio() {
    if (chortenSound) {
        // Try to play and immediately pause to enable audio context
        chortenSound.play().then(() => {
            chortenSound.pause();
            chortenSound.currentTime = 0;
        }).catch(() => {
            // Ignore errors during audio context initialization
        });
    }
    // Remove this listener after first interaction
    document.removeEventListener('click', enableAudio);
}, { once: true });

// Close message button - black fade then redirect to new chorten page
closeButton.addEventListener("click", () => {
    messageDiv.style.display = "none";

    // Store audio state but DON'T stop the music - let it continue playing
    if (chortenSound && !chortenSound.paused) {
        // Store that music should be playing on next page
        sessionStorage.setItem('chortenMusicPlaying', 'true');
        // Don't store time - let it continue naturally

        // Create a global audio reference that can persist
        window.globalChortenAudio = chortenSound;
    }

    // Create black fade overlay
    const fadeOverlay = document.createElement('div');
    fadeOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: black;
        opacity: 0;
        z-index: 1000;
        transition: opacity 0.8s ease-in-out;
    `;
    document.body.appendChild(fadeOverlay);

    // Trigger fade in
    setTimeout(() => {
        fadeOverlay.style.opacity = '1';
    }, 50);

    // Redirect after fade completes (shorter delay for smoother transition)
    setTimeout(() => {
        window.location.href = "newchorten.html";
    }, 900);
});